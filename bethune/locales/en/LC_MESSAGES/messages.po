# English translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid "Hello, world!"
msgstr "Hello, world!"

# 通用
msgid "GenderEnum_male"
msgstr "Male"

msgid "GenderEnum_female"
msgstr "Female"


## 婚姻状况
msgid "MaritalStatusEnum_Divorced"
msgstr "Divorced"

msgid "MaritalStatusEnum_LivingCommonLaw"
msgstr "Living Common Law"

msgid "MaritalStatusEnum_Married"
msgstr "Married"

msgid "MaritalStatusEnum_Separated"
msgstr "Separated"

msgid "MaritalStatusEnum_Single"
msgstr "Single"

msgid "MaritalStatusEnum_Widowed"
msgstr "Widowed"

## 其它通用信息
msgid "Common_other"
msgstr "Other"

msgid "Common_No"
msgstr "No"

msgid "Common_Not"
msgstr "Not"

msgid "Common_Active"
msgstr "Active"

msgid "Common_Suspended"
msgstr "Suspended"



msgid "NumberOfClaimsEnum_zero"
msgstr "0"

msgid "NumberOfClaimsEnum_one"
msgstr "1"

msgid "NumberOfClaimsEnum_twoplus"
msgstr "2+"

msgid "BrokerageUserRoleEnum_broker"
msgstr "Broker"

msgid "BrokerageUserRoleEnum_support"
msgstr "Support"

msgid "success"
msgstr "success"

msgid "Unauthenticated"
msgstr "Authentication failed"

msgid "Unauthorized"
msgstr "Authorization failed"

msgid "User not found"
msgstr "User does not exist"

msgid "English"
msgstr "English"

msgid "French"
msgstr "French"

msgid "Chinese"
msgstr "Chinese"



msgid "user is not active"
msgstr "user is not active"


# 省
msgid "ProvinceEnum_fed"
msgstr "Federal"

msgid "ProvinceEnum_ab"
msgstr "Alberta"

msgid "ProvinceEnum_bc"
msgstr "British Columbia"

msgid "ProvinceEnum_mb"
msgstr "Manitoba"

msgid "ProvinceEnum_nb"
msgstr "New Brunswick"

msgid "ProvinceEnum_nl"
msgstr "Newfoundland and Labrador"

msgid "ProvinceEnum_nt"
msgstr "Northwest Territories"

msgid "ProvinceEnum_ns"
msgstr "Nova Scotia"

msgid "ProvinceEnum_nu"
msgstr "Nunavut"

msgid "ProvinceEnum_on"
msgstr "Ontario"

msgid "ProvinceEnum_pei"
msgstr "Prince Edward Island"

msgid "ProvinceEnum_qc"
msgstr "Quebec"

msgid "ProvinceEnum_sk"
msgstr "Saskatchewan"

msgid "ProvinceEnum_yk"
msgstr "Yukon"


# 市
msgid "BCCityEnum_vi"
msgstr "Victoria"

msgid "BCCityEnum_va"
msgstr "Vancouver"

msgid "BCCityEnum_ke"
msgstr "Kelowna"

msgid "BCCityEnum_pg"
msgstr "Prince George"

msgid "BCCityEnum_ab"
msgstr "Abbotsford"

msgid "BCCityEnum_ka"
msgstr "Kamloops"

msgid "BCCityEnum_ri"
msgstr "Richmond"

msgid "BCCityEnum_su"
msgstr "Surrey"

msgid "BCCityEnum_bu"
msgstr "Burnaby"

msgid "BCCityEnum_nv"
msgstr "North Vancouver"

msgid "BCCityEnum_wv"
msgstr "West Vancouver"

msgid "BCCityEnum_wr"
msgstr "White Rock"

msgid "BCCityEnum_la"
msgstr "Langley"

msgid "BCCityEnum_de"
msgstr "Delta"

msgid "BCCityEnum_nw"
msgstr "New Westminster"

msgid "BCCityEnum_co"
msgstr "Coquitlam"

msgid "BCCityEnum_pm"
msgstr "Port Moody"

msgid "BCCityEnum_na"
msgstr "Nanaimo"

msgid "BCCityEnum_pe"
msgstr "Penticton"

msgid "BCCityEnum_ve"
msgstr "Vernon"



msgid "Freehold Detached House"
msgstr "Freehold Detached House"

msgid "Owner-Occupied Condo"
msgstr "Owner-Occupied Condo"

msgid "Rental"
msgstr "Rental"



msgid "Owner-Occupied"
msgstr "Owner-Occupied"

msgid "Tenant-Occupied"
msgstr "Tenant-Occupied"

msgid "Vacation Use"
msgstr "Vacation Use"

msgid "Secondary Residence"
msgstr "Secondary Residence"

msgid "Immediate Family"
msgstr "Immediate Family"

msgid "Other"
msgstr "Other"



msgid "Natural Gas Furnace"
msgstr "Natural Gas Furnace"

msgid "Electric Baseboard"
msgstr "Electric Baseboard"

msgid "Fireplace"
msgstr "Fireplace"

msgid "Portable Heater"
msgstr "Portable Heater"



msgid "Aluminum Wiring"
msgstr "Aluminum Wiring"

msgid "Copper Wiring"
msgstr "Copper Wiring"

msgid "Knob-and-Tube"
msgstr "Knob-and-Tube"



msgid "Asphalt Shingles"
msgstr "Asphalt Shingles"

msgid "Clay Tiles"
msgstr "Clay Tiles"

msgid "Corrugated Steel"
msgstr "Corrugated Steel"

msgid "Metal Shingles"
msgstr "Metal Shingles"

msgid "Elastomeric"
msgstr "Elastomeric"

msgid "Rubber"
msgstr "Rubber"

msgid "Slate Tiles"
msgstr "Slate Tiles"

msgid "Tar and Gravel"
msgstr "Tar and Gravel"

msgid "Wood Shakes"
msgstr "Wood Shakes"

msgid "Wood Shingles"
msgstr "Wood Shingles"



msgid "200 AMP"
msgstr "200 AMP"

msgid "100 AMP"
msgstr "100 AMP"

msgid "50 AMP"
msgstr "50 AMP"



msgid "Copper"
msgstr "Copper"

msgid "Cast Iron"
msgstr "Cast Iron"

msgid "Galvanized"
msgstr "Galvanized"

msgid "ABS"
msgstr "ABS"

msgid "PEX"
msgstr "PEX"

msgid "PVC"
msgstr "PVC"

msgid "Mixed - Copper/ABS"
msgstr "Mixed - Copper/ABS"

msgid "Mixed - Copper/PVC"
msgstr "Mixed - Copper/PVC"



msgid "Fireproof"
msgstr "Fireproof"

msgid "Wood Frame"
msgstr "Wood Frame"

msgid "Log"
msgstr "Log"



# 建筑类型1
msgid "Highrise"
msgstr "Highrise"

msgid "Highrise with Commercial Units below"
msgstr "Highrise with Commercial Units below"

msgid "Lowrise"
msgstr "Lowrise"

msgid "Lowrise with Commercial Units below"
msgstr "Lowrise with Commercial Units below"

msgid "Townhouse"
msgstr "Townhouse"

msgid "Duplex"
msgstr "Duplex"

msgid "Triplex"
msgstr "Triplex"



# 建筑类型2
msgid "StructureEnum2_bs"
msgstr "Basement Suite"

msgid "StructureEnum2_hdt"
msgstr "House, Duplex, Townhouse"

msgid "StructureEnum2_low"
msgstr "Low-rise 4 stories or less"

msgid "StructureEnum2_high"
msgstr "High-rise 5+ stories"



msgid "Central Furnace Natural Gas"
msgstr "Central Furnace Natural Gas"

msgid "Central Furnace Propane"
msgstr "Central Furnace Propane"

msgid "Solid Fuel Heater"
msgstr "Solid Fuel Heater"

msgid "Space Heater"
msgstr "Space Heater"



msgid "Fully Covered"
msgstr "Fully Covered"

msgid "Partially Covered"
msgstr "Partially Covered"

msgid "None"
msgstr "None"

msgid "Feature Suggestion"
msgstr "Feature Suggestion"

msgid "Usage Issue"
msgstr "Usage Issue"

msgid "Interface Optimization"
msgstr "Interface Optimization"

msgid "Content Error"
msgstr "Content Error"

# Error Messages - Authentication
msgid "error.unauthenticated"
msgstr "Authentication failed"

msgid "error.unauthorized"
msgstr "Insufficient permissions"

msgid "error.user_inactive"
msgstr "User is inactive"

msgid "error.invalid_credentials"
msgstr "Invalid username or password"

msgid "error.token_expired"
msgstr "Token has expired"

# Error Messages - Password
msgid "error.password_expired"
msgstr "Password has expired"

msgid "error.password_invalid"
msgstr "Invalid password"

msgid "error.password_not_match"
msgstr "Password does not match"

msgid "error.old_password_required"
msgstr "Old password is required"

msgid "error.old_password_not_match"
msgstr "Old password is incorrect"

# Error Messages - Data Validation
msgid "error.data_validation"
msgstr "Data validation failed"

msgid "error.missing_required_parameters"
msgstr "Missing required parameters"

msgid "error.invalid_email_format"
msgstr "Invalid email format"

msgid "error.invalid_phone_format"
msgstr "Invalid phone number format"

# Error Messages - Resource Not Found
msgid "error.not_found"
msgstr "Resource not found"

msgid "error.user_not_found"
msgstr "User not found"

msgid "error.permission_not_found"
msgstr "Permission not found"

msgid "error.insurance_company_not_found"
msgstr "Insurance company not found"

msgid "error.broker_not_found"
msgstr "Broker not found"

msgid "error.brokerage_not_found"
msgstr "Brokerage not found"

# Error Messages - Business Logic
msgid "error.user_already_exists"
msgstr "User already exists"

msgid "error.email_already_exists"
msgstr "Email already exists"

msgid "error.brokerage_name_or_email_exists"
msgstr "Brokerage name or contact email already exists"

msgid "error.user_not_broker"
msgstr "User is not a broker"

msgid "error.user_not_brokerage_user"
msgstr "User is not a brokerage user"

msgid "error.invalid_user_type"
msgstr "This type of user cannot log on this site"

# Error Messages - Verification Code
msgid "error.verification_code_error"
msgstr "Verification code error"

msgid "error.verification_code_expired"
msgstr "Verification code has expired"

msgid "error.verification_code_incorrect"
msgstr "Verification code is incorrect"

# Error Messages - OAuth
msgid "error.oauth_info_not_found"
msgstr "OAuth information not found"

msgid "error.oauth_user_mismatch"
msgstr "OAuth user information mismatch"

# Error Messages - File
msgid "error.image_format_error"
msgstr "Image format error"

msgid "error.file_upload_error"
msgstr "File upload failed"

msgid "error.file_size_exceeded"
msgstr "File size exceeded limit"

# Error Messages - System
msgid "error.unexpected_error"
msgstr "Unexpected error"

msgid "error.internal_server_error"
msgstr "Internal server error"

msgid "error.service_unavailable"
msgstr "Service unavailable"

# Success Messages
msgid "success.general"
msgstr "Operation successful"

msgid "success.created"
msgstr "Created successfully"

msgid "success.updated"
msgstr "Updated successfully"

msgid "success.deleted"
msgstr "Deleted successfully"

msgid "success.login"
msgstr "Login successful"

msgid "success.logout"
msgstr "Logout successful"

msgid "success.password_changed"
msgstr "Password changed successfully"

msgid "success.password_reset"
msgstr "Password reset successfully"

msgid "success.email_sent"
msgstr "Email sent successfully"

msgid "success.verification_code_sent"
msgstr "Verification code sent successfully"

msgid "success.user_created"
msgstr "User created successfully"

msgid "success.user_updated"
msgstr "User updated successfully"

msgid "success.broker_registered"
msgstr "Broker registered successfully"

msgid "success.brokerage_created"
msgstr "Brokerage created successfully"

msgid "success.brokerage_approved"
msgstr "Brokerage approved successfully"

# Info Messages
msgid "info.operation_in_progress"
msgstr "Operation in progress"

msgid "info.please_wait"
msgstr "Please wait"

msgid "info.processing"
msgstr "Processing"

msgid "info.pending_approval"
msgstr "Pending approval"

msgid "info.under_review"
msgstr "Under review"

msgid "info.approved"
msgstr "Approved"

msgid "info.rejected"
msgstr "Rejected"



# 车险相关

## 司机熟练度
msgid "DriverProficiencyEnum_Main"
msgstr "Yes, as a main driver"

msgid "DriverProficiencyEnum_Occasional"
msgstr "Yes, as an occasional driver"

## 驾照类别
msgid "DriverLicenseTypeEnum_Class7"
msgstr "Class 7 or Learners Permit"

msgid "DriverLicenseTypeEnum_Newbie"
msgstr "Probationary/Novice or Graduated Licence"

msgid "DriverLicenseTypeEnum_Class5"
msgstr "Class 5 or Full Licence"

msgid "DriverLicenseTypeEnum_NotAbove"
msgstr "None of the above"

## 司机间关系
msgid "DriverRelationshipEnum_Spouse"
msgstr "Spouse"

msgid "DriverRelationshipEnum_Child"
msgstr "Child"

msgid "DriverRelationshipEnum_Parent"
msgstr "Parent"

msgid "DriverRelationshipEnum_Roommate"
msgstr "Roommate"

msgid "DriverRelationshipEnum_Employee"
msgstr "Employee"

## 车辆拥有权状态
msgid "VehicleOwnershipStatusEnum_Lease"
msgstr "Lease"

msgid "VehicleOwnershipStatusEnum_Finance"
msgstr "Finance"

msgid "VehicleOwnershipStatusEnum_FullyOwned"
msgstr "Fully Owned"

## 车辆年度里程数
msgid "VehicleYearlyMileageEnum_Less2500"
msgstr "2,500 kilometers or less"

msgid "VehicleYearlyMileageEnum_Less5000"
msgstr "2,501 to 5,000 kilometers"

msgid "VehicleYearlyMileageEnum_Less7500"
msgstr "5,001 to 7,500 kilometers"

msgid "VehicleYearlyMileageEnum_Less10000"
msgstr "7,501 to 10,000 kilometers"

msgid "VehicleYearlyMileageEnum_Less15000"
msgstr "10,001 to 15,000 kilometers"

msgid "VehicleYearlyMileageEnum_Less20000"
msgstr "15,001 to 20,000 kilometers"

msgid "VehicleYearlyMileageEnum_Less25000"
msgstr "20,001 to 25,000 kilometers"

msgid "VehicleYearlyMileageEnum_Less30000"
msgstr "25,001 to 30,000 kilometers"

msgid "VehicleYearlyMileageEnum_Less40000"
msgstr "30,001 to 40,000 kilometers"

msgid "VehicleYearlyMileageEnum_Greater40000"
msgstr "40,001 kilometers or more"

##保险信息
msgid "LifeTypeEnum_FamilyFinancialPlanning"
msgstr "Family Financial Planning"

msgid "LifeTypeEnum_retirementTaxPlanning"
msgstr "Retirement Tax Planning"

msgid "LifeTypeEnum_assetInheritancePlanning"
msgstr "Asset Inheritance Planning"

msgid "LifeTypeEnum_smeFinancialTaxPlanning"
msgstr "SME Financial Tax Planning"
