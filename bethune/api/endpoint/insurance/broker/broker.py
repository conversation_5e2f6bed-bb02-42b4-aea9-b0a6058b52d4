from datetime import datetime
from os import path
from typing import Annotated
from urllib.parse import urlparse

from fastapi import APIRouter
from fastapi import BackgroundTasks
from fastapi import Depends
from fastapi import Path
from fastapi import Query
from fastapi import Request
from fastapi import Security

from bethune.api.dependencies.authorization import check_not_shared_link
from bethune.api.dto.auth import SendEmailVerificationRequest
from bethune.api.dto.auth import Token
from bethune.api.dto.auth import VerifyVerificationEmailCode
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import PageParams
from bethune.api.dto.base import Pagination
from bethune.api.dto.broker import Broker
from bethune.api.dto.broker import BrokerBusinessCard
from bethune.api.dto.broker import BrokerChangePassword
from bethune.api.dto.broker import BrokerCreate
from bethune.api.dto.broker import BrokerLeadConfig
from bethune.api.dto.broker import BrokerLeadConfigUpdate
from bethune.api.dto.broker import BrokerLeadSummary
from bethune.api.dto.broker import BrokerList
from bethune.api.dto.broker import BrokerResetPassword
from bethune.api.dto.broker import BrokerUpdate
from bethune.api.dto.broker import InsuranceConsultation
from bethune.api.dto.broker import InsuranceConsultationList
from bethune.api.dto.broker import InsuranceConsultationQueryFilter
from bethune.api.dto.broker import PromotionMaterial
from bethune.api.dto.broker import PromotionMaterialList
from bethune.api.dto.broker import PromotionMaterialQueryFilter
from bethune.api.dto.user import UserCreate
from bethune.api.dto.user_feedback import UserFeedback
from bethune.api.dto.user_feedback import UserFeedbackCreate
from bethune.api.endpoint.core.service_context import (
    ServiceContext as CoreServiceContext,
)
from bethune.api.endpoint.insurance.service_context import (
    ServiceContext as InsuranceServiceContext,
)
from bethune.api.endpoint.system.service_context import (
    ServiceContext as SystemServiceContext,
)
from bethune.api.error import UnauthenticatedError
from bethune.db.redis import get_redis
from bethune.error import PasswordNotMatchError
from bethune.error.errors import DataValidationError
from bethune.error.errors import NotFoundError
from bethune.logging import logger
from bethune.model.broker import Broker as BrokerModel
from bethune.model.system import OAuthUserInfo
from bethune.model.system import ROLE_BROKER_ID
from bethune.model.system import ROLE_REFERRAL_BROKER_ID
from bethune.model.system import UserStatus
from bethune.model.system import UserType
from bethune.service.core.ref_code import ReferenceTypeEnum
from bethune.settings import settings
from bethune.util.password import hash_password

api_router = APIRouter(prefix="", tags=["broker"])


@api_router.post("/verification_code")
async def _(
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    verification_request: SendEmailVerificationRequest,
    background_tasks: BackgroundTasks,
):
    await sc_system.user_service.check_user_exists(str(verification_request.email))

    background_tasks.add_task(
        core_sc.verification_code_service.send_verification_email,
        str(verification_request.email),
        4,
    )
    return BaseResponse.ok(verification_request.email)


@api_router.post("/verify_email_code")
async def _(
    sc_core: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    verify_email: VerifyVerificationEmailCode,
):
    await sc_system.user_service.check_user_exists(str(verify_email.email))
    await sc_core.verification_code_service.verify_verification_code(verify_email.email, verify_email.verification_code)
    return BaseResponse.ok(verify_email.email)


@api_router.post("/reset_password_verification_code")
async def _(
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    verification_request: SendEmailVerificationRequest,
    background_tasks: BackgroundTasks,
):
    if await sc_system.user_service.check_user_exists_no_error(str(verification_request.email)) is True:
        background_tasks.add_task(
            core_sc.verification_code_service.send_verification_email,
            str(verification_request.email),
            4,
            True,
        )
    return BaseResponse.ok(verification_request.email)


@api_router.patch("/reset_password", summary="Reset password", response_model=BaseResponse)
async def _(
    broker: BrokerResetPassword,
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
) -> BaseResponse:
    await core_sc.verification_code_service.verify_verification_code(broker.email, broker.verification_code)
    user = sc_system.user_service.get_by_email(broker.email)
    user.password = hash_password(broker.new_password)
    user_updated = sc_system.user_service.update(user)
    if user_updated:
        await core_sc.verification_code_service.delete_verification_code(broker.email)
    return BaseResponse.ok(True)


@api_router.post(
    "/register",
    summary="register broker",
    response_model=BaseResponse[Broker | Token],
)
async def _(
    broker: BrokerCreate,
    sc_core: Annotated[CoreServiceContext, Depends()],
    sc_insurance: Annotated[InsuranceServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
) -> BaseResponse[Broker | Token]:
    logger.info("register broker: {broker}", broker=broker)
    await sc_system.user_service.check_user_exists(broker.email)
    oauth_user_info: OAuthUserInfo | None = None
    if broker.oauth_id is not None:
        data = await get_redis().get(f"bethune:register:oauth_id:{broker.oauth_id}")
        if data is None:
            from bethune.error import raise_oauth_info_not_found
            raise_oauth_info_not_found(oauth_id=broker.oauth_id)
        oauth_user_info = OAuthUserInfo.model_validate_json(data.decode("utf-8"))
        for field in ["created_at", "updated_at", "expires_at"]:
            value = getattr(oauth_user_info, field)
            if isinstance(value, str):
                setattr(
                    oauth_user_info,
                    field,
                    datetime.fromisoformat(value.replace("Z", "+00:00")),
                )
    else:
        await sc_core.verification_code_service.verify_verification_code(broker.email, broker.verification_code)  # type: ignore

    if avatar := broker.avatar:
        avatar = path.basename(urlparse(avatar).path)

    referer_id = None
    if broker.referer_id:
        referer_id = sc_insurance.broker_service.get_broker_by_id(broker.referer_id).user_id

    user_create = UserCreate(
        name=broker.name,
        email=broker.email,
        password=broker.password or "",
        user_type=UserType.SAAS,
        status=UserStatus.ACTIVE,
        mobile=broker.phone,
        avatar=avatar,
        referer_id=referer_id,  # type: ignore
    )
    created_user = sc_system.user_service.create(user_create.to_model())
    role_id = ROLE_BROKER_ID if broker.is_qualified else ROLE_REFERRAL_BROKER_ID
    sc_system.user_service.set_roles(created_user.id, [role_id])  # type: ignore

    created_broker = sc_insurance.broker_service.create_broker(
        broker.to_model(created_user.id, sc_core.reference_code_service.gen_code(ReferenceTypeEnum.BROKER)),  # type: ignore
        is_qualified=broker.is_qualified,
    )
    await sc_core.verification_code_service.delete_verification_code(broker.email)
    if oauth_user_info is not None:
        oauth_user_info.user_id = created_user.id  # type: ignore
        sc_system.oauth_user_info_service.create(oauth_user_info)
        token = sc_system.auth_service.create_token_by_user(created_user)
        return BaseResponse[Token].ok(token)
    return BaseResponse[Broker].ok(Broker.from_model(created_broker))


@api_router.put(
    "/me",
    summary="update broker",
    response_model=BaseResponse[Broker],
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    broker: BrokerUpdate,
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
) -> BaseResponse[Broker]:
    broker_id: int = sc.current_broker.id  # type: ignore
    if avatar := broker.avatar:
        avatar = path.basename(urlparse(avatar).path)
        user = sc.current_user
        user.avatar = avatar
        sc_system.user_service.update(user)

    if broker.is_qualified is not None:
        broker_role_id = ROLE_BROKER_ID if broker.is_qualified else ROLE_REFERRAL_BROKER_ID
        sc_system.user_service.set_roles(sc.current_user.id, [broker_role_id])  # type: ignore

    if payment_method := broker.payment_method:
        payment_method = payment_method.model_dump(exclude_unset=True)
    updated_broker = sc.broker_service.update_broker(
        broker_id,
        broker.model_dump(exclude_unset=True),  # type: ignore
        payment_method=payment_method,
        is_qualified=broker.is_qualified,
    )

    return BaseResponse.ok(Broker.from_model(updated_broker, sc.permissions))


@api_router.patch(
    "/change_password",
    summary="change password",
    response_model=BaseResponse,
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    broker_change_password: BrokerChangePassword,
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
) -> BaseResponse:
    user_id: int = sc.current_broker.user_id  # type: ignore
    user = sc_system.user_service.get_by_id(user_id)
    try:
        changed_password = sc_system.auth_service.change_password(
            user.email,
            broker_change_password.origin_password,
            broker_change_password.new_password,
        )
    except UnauthenticatedError as e:
        from bethune.error import raise_old_password_not_match, raise_old_password_required
        if not broker_change_password.origin_password:
            raise_old_password_required(email=user.email) from e
        raise_old_password_not_match(email=user.email) from e
    return BaseResponse.ok(changed_password)


@api_router.get(
    "/tags",
    summary="get broker tags",
    response_model=BaseResponse[set[str]],
)
async def get_tags(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[set[str]]:
    broker_id: int = sc.current_broker.id  # type: ignore
    tags = await sc.broker_service.get_tags(broker_id)
    return BaseResponse.ok(tags)


@api_router.get(
    "/my-friends",
    summary="get my friends",
    response_model=BaseResponse[Pagination[BrokerList]],
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    query_params: Annotated[PageParams, Query()],
    sc_system: Annotated[SystemServiceContext, Security(SystemServiceContext.create)],
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[Pagination[BrokerList]]:
    user_id: int = sc.current_broker.user_id  # type: ignore
    total, brokers = sc.broker_service.my_friends(
        user_id,
        query_params.offset(),
        query_params.limit(),
    )

    brokers = BrokerList.from_models(brokers)
    for broker in brokers:
        if avatar := sc_system.user_service.get_by_id(broker.user_id).avatar:
            broker.avatar = f"{settings.BETHUNE_SITE_URL}/{settings.UPLOADS_FOLDER}/{settings.AVATARS_FOLDER}/{avatar}"

    data = Pagination[BrokerList].from_items(
        brokers,
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.put(
    "/lead-config",
    summary="set lead config",
    response_model=BaseResponse[BrokerLeadConfig],
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    broker_lead_config: BrokerLeadConfigUpdate,
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[BrokerLeadConfig]:
    lead_config_model, lead_fee_models, profile_model = broker_lead_config.to_model(broker=sc.current_broker)  # type: ignore
    return BaseResponse[BrokerLeadConfig].ok(
        BrokerLeadConfig.from_model(
            *sc.broker_service.update_lead_config(
                sc.current_broker.id,  # type: ignore
                lead_config_model,
                lead_fee_models,
                profile_model,
            )
        )
    )  # type: ignore


@api_router.get(
    "/lead-config",
    summary="get lead config",
    response_model=BaseResponse[BrokerLeadConfig],
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[BrokerLeadConfig]:
    broker_id: int = sc.current_broker.id  # type: ignore
    broker_lead_config = sc.current_broker.lead_config
    broker_profile = sc.current_broker.profile
    broker_lead_fees = sc.broker_service.get_lead_fee_by_broker_id(broker_id)
    return BaseResponse.ok(BrokerLeadConfig.from_model(broker_lead_config, broker_lead_fees, broker_profile))


@api_router.get(
    "/lead-summary",
    summary="get the summary info of lead of current referral broker",
    response_model=BaseResponse[BrokerLeadSummary],
    dependencies=[Security(check_not_shared_link)],
)
async def _(
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[BrokerLeadSummary]:
    return BaseResponse.ok(
        BrokerLeadSummary(
            total_lead_count=sc.lead_service.get_total_lead_count(sc.current_broker),
            total_lead_referral_fee=sc.lead_service.get_total_lead_referral_fee(sc.current_broker) / 100,
        )
    )


@api_router.post(
    "/feedback",
    summary="submit feedback",
    response_model=BaseResponse[UserFeedback],
)
async def submit_feedback(
    feedback: UserFeedbackCreate,
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
) -> BaseResponse[None]:
    """Submit feedback from the broker."""
    user_id: int = sc.current_broker.user_id  # type: ignore
    logger.info(feedback.to_model(user_id=user_id))
    created_user_feedback = sc.user_feedback_service.create(feedback.to_model(user_id=user_id))
    return BaseResponse.ok(created_user_feedback)


@api_router.get(
    "/{uid:str}/business_card",
    summary="get broker's business card",
    response_model=BaseResponse[BrokerBusinessCard],
)
async def get_broker_business_card(
    uid: Annotated[str, Path(description="The unique identifier of the broker")],
    sc: Annotated[InsuranceServiceContext, Depends()],
) -> BaseResponse[BrokerBusinessCard]:
    """Get the business card of a broker by their unique identifier."""
    broker = sc.broker_service.get_by_uid(uid)
    if not broker:
        raise NotFoundError(
            message="Broker not found",
            detail={"uid": uid},
        )

    # TODO add default avatar and description if not exists
    return BaseResponse.ok(BrokerBusinessCard.from_model(broker))


@api_router.get(
    "/promotion_material",
    response_model=BaseResponse[Pagination[PromotionMaterialList]],
)
async def _(
    query_params: Annotated[PageParams, Query()],
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
):
    broker_id: int = sc.current_broker.id  # type: ignore
    total, promotion_materials = sc.promotion_material_service.get_by_example_with_total(
        example=PromotionMaterialQueryFilter(broker_id=broker_id).to_model(),
        offset=query_params.offset(),
        limit=query_params.limit(),
    )

    data = Pagination[PromotionMaterialList].from_items(
        PromotionMaterialList.from_models(promotion_materials),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.post("/promotion_material", response_model=BaseResponse[dict])
async def _(
    request: Request,
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
):
    data = await request.json()
    if not data or "image" not in data:
        raise DataValidationError(message="Invalid image data")

    image, image_url = sc.promotion_material_service.save_base64_image(base64_str=data["image"])
    promotion_material = sc.promotion_material_service.create(
        PromotionMaterial(
            broker_id=sc.current_broker.id,  # type: ignore
            image=image,
        ).to_model()  # type: ignore
    )
    return BaseResponse.ok({"id": promotion_material.id, "image": image, "image_url": image_url})


@api_router.delete("/promotion_material/{id}", response_model=BaseResponse[PromotionMaterial])
async def _(
    id: int,
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
):
    broker_id: int = sc.current_broker.id  # type: ignore
    promotion_material = sc.promotion_material_service.get_by_broker_and_id(broker_id, id)
    sc.promotion_material_service.delete(promotion_material)
    sc.promotion_material_service.delete_material_file(promotion_material.image)
    return BaseResponse.ok(None)


@api_router.post(
    "/{broker_uid}/insurance_consultation",
    response_model=BaseResponse[InsuranceConsultation],
)
async def _(
    broker_uid: str,
    insurance_consultation: InsuranceConsultation,
    background_tasks: BackgroundTasks,
    core_sc: Annotated[CoreServiceContext, Depends()],
    sc: Annotated[InsuranceServiceContext, Depends()],
):
    broker: BrokerModel = sc.broker_service.get_by_uid(broker_uid)
    broker_id: int = broker.id  # type: ignore
    insurance_consultation_created = sc.promotion_material_service.create(insurance_consultation.to_model(broker_id=broker_id))  # type: ignore

    template_context = {
        "insurance_type": None,
        "name": broker.name,
        "link": f"{settings.BETHUNE_SITE_URL}/#/poster_leads",
    }

    background_tasks.add_task(
        core_sc.email_service.send_email,
        subject="🎉 Congratulations! You’ve Received a New Sales Lead",
        recipients=[broker.user.email],
        template_name="broker_lead_notification.html",
        template_context=template_context,
    )
    return BaseResponse.ok(InsuranceConsultation.from_model(insurance_consultation_created))


@api_router.get(
    "/insurance_consultation",
    response_model=BaseResponse[Pagination[InsuranceConsultationList]],
)
async def _(
    query_params: Annotated[PageParams, Query()],
    sc: Annotated[InsuranceServiceContext, Security(InsuranceServiceContext.create)],
):
    broker_id: int = sc.current_broker.id  # type: ignore
    total, insurance_consultations = sc.insurance_consultation_service.get_by_example_with_total(
        example=InsuranceConsultationQueryFilter(broker_id=broker_id).to_model(),
        offset=query_params.offset(),
        limit=query_params.limit(),
    )

    data = Pagination[InsuranceConsultationList].from_items(
        InsuranceConsultationList.from_models(insurance_consultations),
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)
