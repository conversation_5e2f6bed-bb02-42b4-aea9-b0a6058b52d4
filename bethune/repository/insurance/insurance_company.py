from sqlalchemy.exc import NoResultFound
from sqlmodel import select

from bethune.db.session_context import session
from bethune.error import NotFoundError
from bethune.model import InsuranceCompany
from bethune.repository.base import BaseRepository


class InsuranceCompanyRepository(BaseRepository[InsuranceCompany]):

    def __init__(self):
        super().__init__(model_class=InsuranceCompany)

    def get_by_code(self, code: str) -> InsuranceCompany:
        try:
            return session().exec(select(InsuranceCompany).where(InsuranceCompany.code == code)).one()
        except NoResultFound:
            from bethune.error import raise_insurance_company_not_found
            raise_insurance_company_not_found(code=code)

    def get_all(self):
        return session().exec(select(InsuranceCompany).order_by(InsuranceCompany.id.asc())).all()
