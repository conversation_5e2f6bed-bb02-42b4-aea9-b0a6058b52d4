"""
便捷的异常抛出工具
提供类型安全的异常创建函数
"""

from typing import Any

from .errors import (
    BusinessError,
    DataValidationError,
    NotFoundError,
    PasswordExpiredError,
    PasswordInvalidError,
    PasswordNotMatchError,
    UnexpectedError,
    VerificationCodeError,
)
from .messages import ErrorMessages


def raise_unauthenticated(
    message: str | None = None,
    detail: dict | None = None,
    i18n_key: str = ErrorMessages.UNAUTHENTICATED,
    **i18n_params: Any
) -> None:
    """抛出认证失败异常"""
    from bethune.api.error.errors import UnauthenticatedError
    raise UnauthenticatedError(
        err_msg=message or i18n_key,
        detail=detail,
        metadata={"i18n_key": i18n_key, "i18n_params": i18n_params} if i18n_params else None
    )


def raise_unauthorized(
    message: str | None = None,
    detail: dict | None = None,
    i18n_key: str = ErrorMessages.UNAUTHORIZED,
    **i18n_params: Any
) -> None:
    """抛出权限不足异常"""
    from bethune.api.error.errors import UnauthorizedError
    raise UnauthorizedError(
        err_msg=message or i18n_key,
        detail=detail
    )


def raise_not_found(
    resource: str | None = None,
    detail: dict | None = None,
    i18n_key: str = ErrorMessages.NOT_FOUND,
    **i18n_params: Any
) -> None:
    """抛出资源未找到异常"""
    raise NotFoundError(
        message=resource or i18n_key,
        detail=detail,
        i18n_key=i18n_key,
        i18n_params=i18n_params
    )


def raise_user_not_found(
    email: str | None = None,
    user_id: int | None = None
) -> None:
    """抛出用户未找到异常"""
    detail = {}
    if email:
        detail["email"] = email
    if user_id:
        detail["user_id"] = user_id
    
    raise_not_found(
        detail=detail,
        i18n_key=ErrorMessages.USER_NOT_FOUND
    )


def raise_permission_not_found(code: str) -> None:
    """抛出权限未找到异常"""
    raise_not_found(
        detail={"code": code},
        i18n_key=ErrorMessages.PERMISSION_NOT_FOUND,
        permission_code=code
    )


def raise_insurance_company_not_found(code: str) -> None:
    """抛出保险公司未找到异常"""
    raise_not_found(
        detail={"code": code},
        i18n_key=ErrorMessages.INSURANCE_COMPANY_NOT_FOUND,
        company_code=code
    )


def raise_data_validation_error(
    message: str | None = None,
    detail: dict | None = None,
    i18n_key: str = ErrorMessages.DATA_VALIDATION_ERROR,
    **i18n_params: Any
) -> None:
    """抛出数据验证错误异常"""
    raise DataValidationError(
        message=message or i18n_key,
        detail=detail,
        i18n_key=i18n_key,
        i18n_params=i18n_params
    )


def raise_user_already_exists(email: str) -> None:
    """抛出用户已存在异常"""
    raise_data_validation_error(
        detail={"email": email},
        i18n_key=ErrorMessages.USER_ALREADY_EXISTS,
        email=email
    )


def raise_email_already_exists(email: str) -> None:
    """抛出邮箱已存在异常"""
    raise_data_validation_error(
        detail={"email": email},
        i18n_key=ErrorMessages.EMAIL_ALREADY_EXISTS,
        email=email
    )


def raise_brokerage_name_or_email_exists() -> None:
    """抛出经纪公司名称或邮箱已存在异常"""
    raise_data_validation_error(
        i18n_key=ErrorMessages.BROKERAGE_NAME_OR_EMAIL_EXISTS
    )


def raise_password_not_match(email: str | None = None) -> None:
    """抛出密码不匹配异常"""
    detail = {"email": email} if email else None
    raise PasswordNotMatchError(
        detail=detail,
        i18n_key=ErrorMessages.PASSWORD_NOT_MATCH
    )


def raise_old_password_not_match(email: str | None = None) -> None:
    """抛出旧密码不匹配异常"""
    detail = {"email": email} if email else None
    raise PasswordNotMatchError(
        detail=detail,
        i18n_key=ErrorMessages.OLD_PASSWORD_NOT_MATCH
    )


def raise_old_password_required(email: str | None = None) -> None:
    """抛出需要旧密码异常"""
    detail = {"email": email} if email else None
    raise_data_validation_error(
        detail=detail,
        i18n_key=ErrorMessages.OLD_PASSWORD_REQUIRED
    )


def raise_user_not_broker() -> None:
    """抛出用户不是代理人异常"""
    raise_not_found(
        i18n_key=ErrorMessages.USER_NOT_BROKER
    )


def raise_user_not_brokerage_user() -> None:
    """抛出用户不是经纪公司用户异常"""
    raise_unauthenticated(
        i18n_key=ErrorMessages.USER_NOT_BROKERAGE_USER
    )


def raise_invalid_user_type() -> None:
    """抛出无效用户类型异常"""
    raise_unauthenticated(
        i18n_key=ErrorMessages.INVALID_USER_TYPE
    )


def raise_oauth_info_not_found(oauth_id: str) -> None:
    """抛出OAuth信息未找到异常"""
    raise_not_found(
        detail={"oauth_id": oauth_id},
        i18n_key=ErrorMessages.OAUTH_INFO_NOT_FOUND,
        oauth_id=oauth_id
    )


def raise_verification_code_error(
    code: int | None = None,
    message: str | None = None,
    i18n_key: str = ErrorMessages.VERIFICATION_CODE_ERROR
) -> None:
    """抛出验证码错误异常"""
    raise VerificationCodeError(
        code=code,
        message=message or i18n_key,
        i18n_key=i18n_key
    )


def raise_unexpected_error(
    message: str | None = None,
    detail: dict | None = None,
    i18n_key: str = ErrorMessages.UNEXPECTED_ERROR
) -> None:
    """抛出未知错误异常"""
    raise UnexpectedError(
        message=message or i18n_key,
        detail=detail,
        i18n_key=i18n_key
    )
