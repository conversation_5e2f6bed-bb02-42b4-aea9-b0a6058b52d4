from .errors import BusinessError
from .errors import DataValidationError
from .errors import NotFoundError
from .errors import PasswordExpiredError
from .errors import PasswordNotMatchError
from .errors import UnexpectedError
from .errors import VerificationCodeError
from .messages import ErrorMessages, InfoMessages, SuccessMessages
from .exceptions import (
    raise_brokerage_name_or_email_exists,
    raise_data_validation_error,
    raise_email_already_exists,
    raise_insurance_company_not_found,
    raise_invalid_user_type,
    raise_not_found,
    raise_oauth_info_not_found,
    raise_old_password_not_match,
    raise_old_password_required,
    raise_password_not_match,
    raise_permission_not_found,
    raise_unauthenticated,
    raise_unauthorized,
    raise_unexpected_error,
    raise_user_already_exists,
    raise_user_not_broker,
    raise_user_not_brokerage_user,
    raise_user_not_found,
    raise_verification_code_error,
)

__all__ = [
    # 异常类
    "BusinessError",
    "NotFoundError",
    "PasswordExpiredError",
    "PasswordNotMatchError",
    "DataValidationError",
    "UnexpectedError",
    "VerificationCodeError",
    # 消息常量
    "ErrorMessages",
    "InfoMessages",
    "SuccessMessages",
    # 便捷异常函数
    "raise_brokerage_name_or_email_exists",
    "raise_data_validation_error",
    "raise_email_already_exists",
    "raise_insurance_company_not_found",
    "raise_invalid_user_type",
    "raise_not_found",
    "raise_oauth_info_not_found",
    "raise_old_password_not_match",
    "raise_old_password_required",
    "raise_password_not_match",
    "raise_permission_not_found",
    "raise_unauthenticated",
    "raise_unauthorized",
    "raise_unexpected_error",
    "raise_user_already_exists",
    "raise_user_not_broker",
    "raise_user_not_brokerage_user",
    "raise_user_not_found",
    "raise_verification_code_error",
]
