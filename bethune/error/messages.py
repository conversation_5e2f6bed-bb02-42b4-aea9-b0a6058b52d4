"""
异常消息常量定义
统一管理所有异常的国际化消息键
"""

class ErrorMessages:
    """异常消息常量类"""
    
    # 认证相关
    UNAUTHENTICATED = "error.unauthenticated"
    UNAUTHORIZED = "error.unauthorized"
    USER_INACTIVE = "error.user_inactive"
    INVALID_CREDENTIALS = "error.invalid_credentials"
    TOKEN_EXPIRED = "error.token_expired"
    
    # 密码相关
    PASSWORD_EXPIRED = "error.password_expired"
    PASSWORD_INVALID = "error.password_invalid"
    PASSWORD_NOT_MATCH = "error.password_not_match"
    OLD_PASSWORD_REQUIRED = "error.old_password_required"
    OLD_PASSWORD_NOT_MATCH = "error.old_password_not_match"
    
    # 数据验证相关
    DATA_VALIDATION_ERROR = "error.data_validation"
    MISSING_REQUIRED_PARAMETERS = "error.missing_required_parameters"
    INVALID_EMAIL_FORMAT = "error.invalid_email_format"
    INVALID_PHONE_FORMAT = "error.invalid_phone_format"
    
    # 资源不存在
    NOT_FOUND = "error.not_found"
    USER_NOT_FOUND = "error.user_not_found"
    PERMISSION_NOT_FOUND = "error.permission_not_found"
    INSURANCE_COMPANY_NOT_FOUND = "error.insurance_company_not_found"
    BROKER_NOT_FOUND = "error.broker_not_found"
    BROKERAGE_NOT_FOUND = "error.brokerage_not_found"
    
    # 业务逻辑相关
    USER_ALREADY_EXISTS = "error.user_already_exists"
    EMAIL_ALREADY_EXISTS = "error.email_already_exists"
    BROKERAGE_NAME_OR_EMAIL_EXISTS = "error.brokerage_name_or_email_exists"
    USER_NOT_BROKER = "error.user_not_broker"
    USER_NOT_BROKERAGE_USER = "error.user_not_brokerage_user"
    INVALID_USER_TYPE = "error.invalid_user_type"
    
    # 验证码相关
    VERIFICATION_CODE_ERROR = "error.verification_code_error"
    VERIFICATION_CODE_EXPIRED = "error.verification_code_expired"
    VERIFICATION_CODE_INCORRECT = "error.verification_code_incorrect"
    
    # OAuth相关
    OAUTH_INFO_NOT_FOUND = "error.oauth_info_not_found"
    OAUTH_USER_MISMATCH = "error.oauth_user_mismatch"
    
    # 文件相关
    IMAGE_FORMAT_ERROR = "error.image_format_error"
    FILE_UPLOAD_ERROR = "error.file_upload_error"
    FILE_SIZE_EXCEEDED = "error.file_size_exceeded"
    
    # 系统错误
    UNEXPECTED_ERROR = "error.unexpected_error"
    INTERNAL_SERVER_ERROR = "error.internal_server_error"
    SERVICE_UNAVAILABLE = "error.service_unavailable"
    
    # 权限相关
    INSUFFICIENT_PERMISSIONS = "error.insufficient_permissions"
    SCOPE_REQUIRED = "error.scope_required"
    ROLE_REQUIRED = "error.role_required"
    
    # 业务状态相关
    INVALID_STATUS_TRANSITION = "error.invalid_status_transition"
    OPERATION_NOT_ALLOWED = "error.operation_not_allowed"
    RESOURCE_LOCKED = "error.resource_locked"
    
    # 第三方服务相关
    EMAIL_SEND_FAILED = "error.email_send_failed"
    SMS_SEND_FAILED = "error.sms_send_failed"
    EXTERNAL_SERVICE_ERROR = "error.external_service_error"


class SuccessMessages:
    """成功消息常量类"""
    
    SUCCESS = "success.general"
    CREATED = "success.created"
    UPDATED = "success.updated"
    DELETED = "success.deleted"
    
    # 认证相关
    LOGIN_SUCCESS = "success.login"
    LOGOUT_SUCCESS = "success.logout"
    PASSWORD_CHANGED = "success.password_changed"
    PASSWORD_RESET = "success.password_reset"
    
    # 邮件相关
    EMAIL_SENT = "success.email_sent"
    VERIFICATION_CODE_SENT = "success.verification_code_sent"
    
    # 用户相关
    USER_CREATED = "success.user_created"
    USER_UPDATED = "success.user_updated"
    USER_ACTIVATED = "success.user_activated"
    USER_DEACTIVATED = "success.user_deactivated"
    
    # 经纪相关
    BROKER_REGISTERED = "success.broker_registered"
    BROKERAGE_CREATED = "success.brokerage_created"
    BROKERAGE_APPROVED = "success.brokerage_approved"


class InfoMessages:
    """信息提示常量类"""
    
    # 操作提示
    OPERATION_IN_PROGRESS = "info.operation_in_progress"
    PLEASE_WAIT = "info.please_wait"
    PROCESSING = "info.processing"
    
    # 状态提示
    PENDING_APPROVAL = "info.pending_approval"
    UNDER_REVIEW = "info.under_review"
    APPROVED = "info.approved"
    REJECTED = "info.rejected"
    
    # 系统维护
    SYSTEM_MAINTENANCE = "info.system_maintenance"
    SERVICE_TEMPORARILY_UNAVAILABLE = "info.service_temporarily_unavailable"
