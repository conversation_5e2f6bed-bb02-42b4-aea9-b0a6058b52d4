# 异常国际化机制使用指南

## 🎯 概述

本项目实现了完整的异常国际化机制，支持中文、英文、法文三种语言的错误消息自动翻译。

## 🏗️ 架构设计

### 1. 核心组件

- **`BusinessError`**: 支持国际化的异常基类
- **`ErrorMessages`**: 异常消息常量定义
- **`exceptions.py`**: 便捷的异常抛出工具
- **翻译文件**: 三语言错误消息翻译

### 2. 国际化流程

```
异常抛出 → 获取i18n_key → fastapi-babel翻译 → 返回本地化消息
```

## 📋 使用方法

### 1. 使用便捷异常函数（推荐）

```python
from bethune.error import (
    raise_user_not_found,
    raise_brokerage_name_or_email_exists,
    raise_password_not_match,
    raise_data_validation_error
)

# 用户不存在
raise_user_not_found(email="<EMAIL>")

# 经纪公司名称或邮箱已存在
raise_brokerage_name_or_email_exists()

# 密码不匹配
raise_password_not_match(email="<EMAIL>")

# 数据验证错误（带参数）
raise_data_validation_error(
    i18n_key=ErrorMessages.INVALID_EMAIL_FORMAT,
    email="invalid-email"
)
```

### 2. 直接使用异常类

```python
from bethune.error import NotFoundError, ErrorMessages

# 使用国际化键
raise NotFoundError(
    i18n_key=ErrorMessages.USER_NOT_FOUND,
    detail={"email": "<EMAIL>"}
)

# 使用传统方式（会自动翻译）
raise NotFoundError("User not found")
```

### 3. 带参数的国际化消息

```python
from bethune.error import raise_data_validation_error, ErrorMessages

# 消息模板："{field} is required"
raise_data_validation_error(
    i18n_key="error.field_required",
    i18n_params={"field": "email"},
    detail={"field": "email"}
)
```

## 🌍 多语言支持

### 支持的语言

- **中文 (zh)**: 简体中文
- **英文 (en)**: 英语
- **法文 (fr)**: 法语

### 语言检测机制

1. 请求头 `Accept-Language`
2. 请求状态中的语言设置
3. 默认语言回退

### 翻译文件位置

```
bethune/locales/
├── zh/LC_MESSAGES/messages.po  # 中文翻译
├── en/LC_MESSAGES/messages.po  # 英文翻译
└── fr/LC_MESSAGES/messages.po  # 法文翻译
```

## 📝 消息常量

### 错误消息类别

```python
from bethune.error import ErrorMessages

# 认证相关
ErrorMessages.UNAUTHENTICATED
ErrorMessages.UNAUTHORIZED
ErrorMessages.TOKEN_EXPIRED

# 密码相关
ErrorMessages.PASSWORD_NOT_MATCH
ErrorMessages.OLD_PASSWORD_REQUIRED

# 数据验证
ErrorMessages.DATA_VALIDATION_ERROR
ErrorMessages.EMAIL_ALREADY_EXISTS

# 资源不存在
ErrorMessages.USER_NOT_FOUND
ErrorMessages.PERMISSION_NOT_FOUND
```

### 成功消息

```python
from bethune.error import SuccessMessages

SuccessMessages.LOGIN_SUCCESS
SuccessMessages.USER_CREATED
SuccessMessages.PASSWORD_CHANGED
```

## 🔧 实际应用示例

### API层异常处理

```python
@api_router.post("/brokerage")
async def create_brokerage(brokerage: BrokerageCreatingRequest):
    try:
        return BaseResponse.ok(
            BrokerageResponse.from_model(
                sc.brokerage_service.create(brokerage.to_model())
            )
        )
    except IntegrityError:
        # 使用国际化异常
        raise_brokerage_name_or_email_exists()
```

### Repository层异常处理

```python
def get_by_email(self, email: str) -> User:
    try:
        return session().exec(select(User).where(User.email == email)).one()
    except NoResultFound:
        # 使用国际化异常
        raise_user_not_found(email=email)
```

### Service层异常处理

```python
def change_password(self, email: str, old_password: str, new_password: str):
    try:
        user = self.authenticate_user(email, old_password)
        # 更新密码逻辑
        return user
    except UnauthenticatedError as e:
        # 转换为更具体的异常
        raise_old_password_not_match(email=email) from e
```

## 🎨 最佳实践

### 1. 优先使用便捷函数

```python
# ✅ 推荐
raise_user_not_found(email="<EMAIL>")

# ❌ 不推荐
raise NotFoundError("User not found", detail={"email": "<EMAIL>"})
```

### 2. 保持异常信息的一致性

```python
# ✅ 使用统一的消息键
raise_data_validation_error(i18n_key=ErrorMessages.EMAIL_ALREADY_EXISTS)

# ❌ 硬编码消息
raise DataValidationError("邮箱已存在")
```

### 3. 提供详细的上下文信息

```python
# ✅ 包含有用的调试信息
raise_user_not_found(email="<EMAIL>")

# ❌ 缺少上下文
raise_user_not_found()
```

### 4. 正确的异常链

```python
# ✅ 保持异常链
try:
    # 某些操作
    pass
except SomeException as e:
    raise_custom_error() from e

# ❌ 丢失原始异常信息
except SomeException:
    raise_custom_error()
```

## 🧪 测试支持

### 测试异常消息

```python
def test_user_not_found_exception():
    with pytest.raises(NotFoundError) as exc_info:
        raise_user_not_found(email="<EMAIL>")
    
    assert exc_info.value.i18n_key == ErrorMessages.USER_NOT_FOUND
    assert exc_info.value.detail["email"] == "<EMAIL>"
```

### 测试多语言消息

```python
def test_multilingual_error_messages(client):
    # 测试中文
    response = client.get("/api/test", headers={"Accept-Language": "zh-CN"})
    assert "用户不存在" in response.json()["message"]
    
    # 测试英文
    response = client.get("/api/test", headers={"Accept-Language": "en-US"})
    assert "User not found" in response.json()["message"]
```

## 🔄 迁移指南

### 从旧异常迁移

```python
# 旧方式
raise DataValidationError("经纪公司名称或联系邮箱已存在")

# 新方式
raise_brokerage_name_or_email_exists()
```

### 批量替换建议

1. 识别硬编码的异常消息
2. 在 `ErrorMessages` 中定义对应常量
3. 添加翻译到三个语言文件
4. 创建便捷函数（如需要）
5. 替换原有异常抛出代码

## 📚 扩展指南

### 添加新的错误类型

1. 在 `ErrorMessages` 中定义常量
2. 添加翻译到三个语言文件
3. 在 `exceptions.py` 中创建便捷函数
4. 更新 `__init__.py` 导出

### 添加新语言支持

1. 创建新的语言目录和翻译文件
2. 更新 `LanguageEnum`
3. 配置 fastapi-babel 支持新语言
4. 翻译所有消息键
