"""
异常国际化机制演示示例
展示如何在实际项目中使用国际化异常
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from bethune.error import (
    BusinessError,
    ErrorMessages,
    raise_user_not_found,
    raise_brokerage_name_or_email_exists,
    raise_password_not_match,
    raise_data_validation_error,
    raise_unauthorized,
)


# 示例数据模型
class UserCreate(BaseModel):
    email: str
    password: str
    name: str


class BrokerageCreate(BaseModel):
    name: str
    contact_email: str
    contact_phone: str


# 模拟的服务层
class UserService:
    """用户服务示例"""
    
    def __init__(self):
        self.users = {}  # 模拟数据库
    
    def create_user(self, user_data: UserCreate):
        """创建用户"""
        if user_data.email in self.users:
            # 使用国际化异常
            raise_data_validation_error(
                i18n_key=ErrorMessages.EMAIL_ALREADY_EXISTS,
                detail={"email": user_data.email},
                email=user_data.email
            )
        
        # 模拟创建用户
        self.users[user_data.email] = user_data
        return user_data
    
    def get_user_by_email(self, email: str):
        """根据邮箱获取用户"""
        if email not in self.users:
            # 使用便捷异常函数
            raise_user_not_found(email=email)
        
        return self.users[email]
    
    def authenticate_user(self, email: str, password: str):
        """用户认证"""
        user = self.get_user_by_email(email)  # 可能抛出用户不存在异常
        
        if user.password != password:
            # 使用便捷异常函数
            raise_password_not_match(email=email)
        
        return user


class BrokerageService:
    """经纪公司服务示例"""
    
    def __init__(self):
        self.brokerages = {}  # 模拟数据库
    
    def create_brokerage(self, brokerage_data: BrokerageCreate):
        """创建经纪公司"""
        # 检查名称是否已存在
        for brokerage in self.brokerages.values():
            if (brokerage.name == brokerage_data.name or 
                brokerage.contact_email == brokerage_data.contact_email):
                # 使用国际化异常
                raise_brokerage_name_or_email_exists()
        
        # 模拟创建经纪公司
        brokerage_id = len(self.brokerages) + 1
        self.brokerages[brokerage_id] = brokerage_data
        return brokerage_data


# FastAPI应用示例
app = FastAPI(title="异常国际化演示")

# 服务实例
user_service = UserService()
brokerage_service = BrokerageService()


# 全局异常处理器
@app.exception_handler(BusinessError)
async def business_error_handler(request: Request, exc: BusinessError):
    """业务异常处理器"""
    return JSONResponse(
        status_code=400,
        content={
            "code": exc.code,
            "message": exc.message,  # 已经是国际化后的消息
            "detail": exc.detail,
            "i18n_key": exc.i18n_key,  # 可选：返回国际化键用于前端处理
        }
    )


# API端点示例
@app.post("/users")
async def create_user(user_data: UserCreate):
    """创建用户API"""
    try:
        user = user_service.create_user(user_data)
        return {"message": "User created successfully", "user": user}
    except BusinessError:
        # 异常会被全局处理器捕获并返回国际化消息
        raise


@app.get("/users/{email}")
async def get_user(email: str):
    """获取用户API"""
    try:
        user = user_service.get_user_by_email(email)
        return {"user": user}
    except BusinessError:
        raise


@app.post("/auth/login")
async def login(email: str, password: str):
    """用户登录API"""
    try:
        user = user_service.authenticate_user(email, password)
        return {"message": "Login successful", "user": user}
    except BusinessError:
        raise


@app.post("/brokerages")
async def create_brokerage(brokerage_data: BrokerageCreate):
    """创建经纪公司API"""
    try:
        brokerage = brokerage_service.create_brokerage(brokerage_data)
        return {"message": "Brokerage created successfully", "brokerage": brokerage}
    except BusinessError:
        raise


@app.get("/demo/errors")
async def demo_errors():
    """演示各种异常类型"""
    error_demos = []
    
    # 演示不同类型的异常
    try:
        raise_user_not_found(email="<EMAIL>")
    except BusinessError as e:
        error_demos.append({
            "type": "user_not_found",
            "code": e.code,
            "message": e.message,
            "i18n_key": e.i18n_key
        })
    
    try:
        raise_brokerage_name_or_email_exists()
    except BusinessError as e:
        error_demos.append({
            "type": "brokerage_exists",
            "code": e.code,
            "message": e.message,
            "i18n_key": e.i18n_key
        })
    
    try:
        raise_password_not_match(email="<EMAIL>")
    except BusinessError as e:
        error_demos.append({
            "type": "password_not_match",
            "code": e.code,
            "message": e.message,
            "i18n_key": e.i18n_key
        })
    
    try:
        raise_unauthorized()
    except BusinessError as e:
        error_demos.append({
            "type": "unauthorized",
            "code": e.code,
            "message": e.message,
            "i18n_key": e.i18n_key
        })
    
    return {"error_demos": error_demos}


# 多语言测试端点
@app.get("/demo/multilingual")
async def demo_multilingual(request: Request):
    """演示多语言异常消息"""
    # 获取请求的语言设置
    accept_language = request.headers.get("Accept-Language", "en")
    
    try:
        # 抛出一个异常来演示国际化
        raise_user_not_found(email="<EMAIL>")
    except BusinessError as e:
        return {
            "accept_language": accept_language,
            "error": {
                "code": e.code,
                "message": e.message,  # 根据Accept-Language自动翻译
                "i18n_key": e.i18n_key,
                "detail": e.detail
            }
        }


if __name__ == "__main__":
    import uvicorn
    
    print("🚀 启动异常国际化演示服务...")
    print("📖 访问 http://localhost:8000/docs 查看API文档")
    print("🌍 测试多语言:")
    print("   - 中文: curl -H 'Accept-Language: zh-CN' http://localhost:8000/demo/multilingual")
    print("   - 英文: curl -H 'Accept-Language: en-US' http://localhost:8000/demo/multilingual")
    print("   - 法文: curl -H 'Accept-Language: fr-FR' http://localhost:8000/demo/multilingual")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
